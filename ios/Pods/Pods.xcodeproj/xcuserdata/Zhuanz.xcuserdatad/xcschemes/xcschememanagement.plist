<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>SchemeUserState</key>
	<dict>
		<key>DKImagePickerController-DKImagePickerController.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>DKImagePickerController.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>DKPhotoGallery-DKPhotoGallery.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>DKPhotoGallery.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>Flutter.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>OrderedSet-OrderedSet_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>OrderedSet.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>Pods-Runner.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>Pods-RunnerTests.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>SDWebImage-SDWebImage.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>SDWebImage.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>SwiftyGif-SwiftyGif.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>SwiftyGif.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>audio_service.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>audio_session.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>audioplayers_darwin.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>battery_plus-battery_plus_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>battery_plus.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>connectivity_plus-connectivity_plus_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>connectivity_plus.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>device_info_plus-device_info_plus_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>device_info_plus.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>file_picker-file_picker_ios_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>file_picker.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>flutter_file_dialog.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>flutter_inappwebview_ios-flutter_inappwebview_ios_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>flutter_inappwebview_ios.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>flutter_tts.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>fluttertoast-fluttertoast_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>fluttertoast.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>in_app_purchase_storekit-in_app_purchase_storekit_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>in_app_purchase_storekit.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>just_audio.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>package_info_plus-package_info_plus_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>package_info_plus.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>path_provider_foundation-path_provider_foundation_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>path_provider_foundation.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>permission_handler_apple-permission_handler_apple_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>permission_handler_apple.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>pointer_interceptor_ios-pointer_interceptor_ios_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>pointer_interceptor_ios.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>receive_sharing_intent.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>saver_gallery-saver_gallery.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>saver_gallery.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>share_plus-share_plus_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>share_plus.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>shared_preferences_foundation-shared_preferences_foundation_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>shared_preferences_foundation.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>sqflite_darwin-sqflite_darwin_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>sqflite_darwin.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>url_launcher_ios-url_launcher_ios_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>url_launcher_ios.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>wakelock_plus-wakelock_plus_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>wakelock_plus.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
	</dict>
	<key>SuppressBuildableAutocreation</key>
	<dict/>
</dict>
</plist>
