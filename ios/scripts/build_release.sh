#!/bin/bash

# iOS Release Build Script for DassoShu Reader
# This script optimizes the iOS build for App Store submission

set -e

echo "🚀 Starting iOS Release Build for DassoShu Reader..."

# Navigate to project root
cd "$(dirname "$0")/../.."

# Clean previous builds
echo "🧹 Cleaning previous builds..."
flutter clean
rm -rf ios/build
rm -rf build

# Get dependencies
echo "📦 Getting Flutter dependencies..."
flutter pub get

# Build for iOS release
echo "🔨 Building iOS release..."
flutter build ios --release --no-codesign

# Build for iOS device (with codesign)
echo "📱 Building for iOS device..."
flutter build ios --release

echo "✅ iOS Release Build Complete!"
echo "📍 Build output: build/ios/iphoneos/Runner.app"
echo ""
echo "📋 Next Steps:"
echo "1. Open ios/Runner.xcworkspace in Xcode"
echo "2. Select 'Any iOS Device' as target"
echo "3. Product → Archive for App Store submission"
echo "4. Use Xcode Organizer to upload to App Store Connect"
