import 'dart:io';
import 'package:path/path.dart' as path;

import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/dao/book.dart';
import 'package:dasso_reader/l10n/generated/L10n.dart';
import 'package:dasso_reader/main.dart';
import 'package:dasso_reader/models/book.dart';
import 'package:dasso_reader/page/home_page.dart';
import 'package:dasso_reader/page/iap_page.dart';
import 'package:dasso_reader/providers/ai_chat.dart';
import 'package:dasso_reader/providers/book_list.dart';
import 'package:dasso_reader/service/convert_to_epub/txt/convert_from_txt.dart';
import 'package:dasso_reader/service/iap_service.dart';
import 'package:dasso_reader/utils/env_var.dart';
import 'package:dasso_reader/utils/get_path/get_base_path.dart';
import 'package:dasso_reader/page/reading_page.dart';
import 'package:dasso_reader/utils/import_book.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'package:dasso_reader/utils/toast/common.dart';
import 'package:dasso_reader/utils/webView/webview_console_message.dart';
import 'package:dasso_reader/utils/webView/webview_initial_variable.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'book_player/book_player_server.dart';

HeadlessInAppWebView? headlessInAppWebView;

/// import book list and **delete file**
void importBookList(List<File> fileList, BuildContext context, WidgetRef ref) {
  try {
    final allowBookExtensions = ['epub', 'mobi', 'azw3', 'fb2', 'txt'];

    AnxLog.info('Starting importBookList with ${fileList.length} files');
    AnxLog.info('File paths: ${fileList.map((f) => f.path).toList()}');

    List<File> supportedFiles = fileList.where((file) {
      final extension = file.path.split('.').last.toLowerCase();
      final isSupported = allowBookExtensions.contains(extension);
      AnxLog.info(
        'File ${file.path} - Extension: $extension, Supported: $isSupported',
      );
      return isSupported;
    }).toList();

    List<File> unsupportedFiles = fileList.where((file) {
      final extension = file.path.split('.').last.toLowerCase();
      return !allowBookExtensions.contains(extension);
    }).toList();

    AnxLog.info(
      'Supported files: ${supportedFiles.length}, Unsupported files: ${unsupportedFiles.length}',
    );

    // delete unsupported files
    for (var file in unsupportedFiles) {
      try {
        AnxLog.info('Deleting unsupported file: ${file.path}');
        file.deleteSync();
      } catch (e) {
        AnxLog.warning('Failed to delete unsupported file ${file.path}: $e');
      }
    }

    Widget bookItem(String path, Widget icon) {
      return Row(
        children: [
          SizedBox(
            width: 24,
            height: 24,
            child: icon,
          ),
          Expanded(
            child: Text(
              path.split('/').last,
              style: TextStyle(
                fontWeight: FontWeight.w300,
                color: DesignSystem.getSettingsTextColor(
                  context,
                  isPrimary: false,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
        ],
      );
    }

    showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        String currentHandlingFile = '';
        List<String> errorFiles = [];

        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: Text(
                L10n.of(context).import_n_books_selected(fileList.length),
                style: TextStyle(
                  color: DesignSystem.getSettingsTextColor(
                    context,
                    isPrimary: true,
                  ),
                ),
              ),
              content: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      L10n.of(context).import_support_types(
                        allowBookExtensions.join(' / '),
                      ),
                      style: TextStyle(
                        color: DesignSystem.getSettingsTextColor(
                          context,
                          isPrimary: false,
                        ),
                      ),
                    ),
                    const SizedBox(height: 10),
                    if (unsupportedFiles.isNotEmpty)
                      Text(
                        L10n.of(context).import_n_books_not_support(
                          unsupportedFiles.length,
                        ),
                        style: TextStyle(
                          color: DesignSystem.getSettingsTextColor(
                            context,
                            isPrimary: false,
                          ),
                        ),
                      ),
                    const SizedBox(height: 20),
                    for (var file in unsupportedFiles)
                      bookItem(file.path, const Icon(Icons.error)),
                    for (var file in supportedFiles)
                      file.path == currentHandlingFile
                          ? bookItem(
                              file.path,
                              Container(
                                padding: const EdgeInsets.all(3),
                                width: 20,
                                height: 20,
                                child: const CircularProgressIndicator(),
                              ),
                            )
                          : bookItem(
                              file.path,
                              errorFiles.contains(file.path)
                                  ? const Icon(Icons.error)
                                  : const Icon(Icons.done),
                            ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                    for (var file in supportedFiles) {
                      file.deleteSync();
                    }
                  },
                  child: Text(
                    L10n.of(context).common_cancel,
                    style: TextStyle(
                      color: DesignSystem.getSettingsTextColor(
                        context,
                        isPrimary: false,
                      ),
                    ),
                  ),
                ),
                if (supportedFiles.isNotEmpty)
                  TextButton(
                    onPressed: () async {
                      for (var file in supportedFiles) {
                        AnxToast.show(file.path.split('/').last);
                        setState(() {
                          currentHandlingFile = file.path;
                        });
                        // try {
                        await importBook(file, ref);
                        // } catch (e) {
                        //   setState(() {
                        //     errorFiles.add(file.path);
                        //   });
                        // }
                      }
                      Navigator.of(navigatorKey.currentContext!).pop('dialog');
                    },
                    child: Text(
                      L10n.of(context)
                          .import_import_n_books(supportedFiles.length),
                      style: TextStyle(
                        color: DesignSystem.getSettingsTextColor(
                          context,
                          isPrimary: true,
                        ),
                      ),
                    ),
                  ),
              ],
            );
          },
        );
      },
    );
  } catch (e, stackTrace) {
    AnxLog.severe('Error in importBookList: $e\nStack trace: $stackTrace');

    // Show error to user
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Import failed: $e'),
          backgroundColor: Theme.of(context).colorScheme.error,
          duration: const Duration(seconds: 5),
        ),
      );
    }
  }
}

Future<void> importBook(File file, WidgetRef ref) async {
  try {
    AnxLog.info('Starting importBook for: ${file.path}');

    // Verify file exists
    if (!await file.exists()) {
      throw Exception('File does not exist: ${file.path}');
    }

    final extension = file.path.split('.').last.toLowerCase();
    AnxLog.info('File extension: $extension');

    if (extension == 'txt') {
      AnxLog.info('Converting TXT file to EPUB');
      final tempFile = await convertFromTxt(file);
      file.deleteSync();
      file = tempFile;
      AnxLog.info('TXT conversion complete');
    }

    AnxLog.info('Extracting book metadata...');
    await getBookMetadata(file, ref: ref);

    AnxLog.info('Refreshing book list...');
    ref.read(bookListProvider.notifier).refresh();

    AnxLog.info('Book import completed successfully');
  } catch (e, stackTrace) {
    AnxLog.severe('Error in importBook: $e\nStack trace: $stackTrace');

    // Clean up file if it exists
    try {
      if (await file.exists()) {
        await file.delete();
        AnxLog.info('Cleaned up temporary file after error');
      }
    } catch (cleanupError) {
      AnxLog.warning('Failed to clean up file after error: $cleanupError');
    }

    // Re-throw with context
    throw Exception('Failed to import book: $e');
  }
}

Future<void> pushToReadingPage(
  WidgetRef ref,
  BuildContext context,
  Book book, {
  String? cfi,
}) async {
  if (book.isDeleted) {
    AnxToast.show(L10n.of(context).book_deleted);
    return;
  }
  if (EnvVar.isAppStore) {
    if (!IAPService().isFeatureAvailable) {
      Navigator.of(context).push(
        CupertinoPageRoute<void>(
          builder: (context) => const IAPPage(),
        ),
      );
      return;
    }
  }
  ref.read(aiChatProvider.notifier).clear();
  await Navigator.push(
    context,
    CupertinoPageRoute<void>(
      builder: (context) => ReadingPage(
        key: readingPageKey,
        book: book,
        cfi: cfi,
      ),
    ),
  );
}

Future<void> openBook(BuildContext context, Book book, WidgetRef ref) async {
  await pushToReadingPage(ref, context, book);
}

void updateBookRating(Book book, double rating) {
  book.rating = rating;
  updateBook(book);
}

Future<void> resetBookCover(Book book) async {
  File file = File(book.fileFullPath);
  getBookMetadata(file);
}

Future<void> saveBook(
  File file,
  String title,
  String author,
  String description,
  String cover, {
  Book? provideBook,
}) async {
  final newBookName =
      '${title.length > 20 ? title.substring(0, 20) : title}-${DateTime.now().millisecondsSinceEpoch}'
          .replaceAll(RegExp(r'[<>:"/\\|?*]'), '_')
          .replaceAll('\n', '')
          .replaceAll('\r', '')
          .trim();

  final extension = file.path.split('.').last;

  final dbFilePath = 'file/$newBookName.$extension';
  final filePath = getBasePath(dbFilePath);
  String? dbCoverPath = 'cover/$newBookName';
  // final coverPath = getBasePath(dbCoverPath);

  await file.copy(filePath);
  // remove cached file
  file.delete();

  dbCoverPath = await saveImageToLocal(cover, dbCoverPath);

  Book book = Book(
    id: provideBook != null ? provideBook.id : -1,
    title: title,
    coverPath: dbCoverPath,
    filePath: dbFilePath,
    lastReadPosition: '',
    readingPercentage: 0,
    author: author,
    isDeleted: false,
    rating: 0.0,
    createTime: DateTime.now(),
    updateTime: DateTime.now(),
  );

  book.id = await insertBook(book);
  AnxToast.show('Import successful');
  headlessInAppWebView?.dispose();
  headlessInAppWebView = null;
  return;
}

Future<void> getBookMetadata(
  File file, {
  Book? book,
  WidgetRef? ref,
}) async {
  try {
    AnxLog.info('Starting getBookMetadata for file: ${file.path}');

    // Verify file exists and is readable
    if (!await file.exists()) {
      throw Exception('File does not exist: ${file.path}');
    }

    final fileSize = await file.length();
    AnxLog.info('File size: $fileSize bytes');

    if (fileSize == 0) {
      throw Exception('File is empty: ${file.path}');
    }

    // iOS fallback: Use simplified metadata extraction to avoid WebView timeout issues
    if (Platform.isIOS) {
      AnxLog.info('Using iOS fallback metadata extraction');
      final fileName = path.basenameWithoutExtension(file.path);
      final title = fileName.replaceAll('_', ' ').replaceAll('-', ' ');
      const author = 'Unknown Author';
      const description = '';
      const cover = ''; // No cover for fallback

      AnxLog.info('iOS fallback metadata: title=$title, author=$author');
      await saveBook(file, title, author, description, cover);
      ref?.read(bookListProvider.notifier).refresh();
      return;
    }

    String serverFileName = Server().setTempFile(file);
    AnxLog.info('Server filename set: $serverFileName');

    String cfi = '';

    String indexHtmlPath =
        'http://localhost:${Server().port}/foliate-js/index.html';

    String bookUrl = 'http://localhost:${Server().port}/$serverFileName';
    AnxLog.info('Starting EPUB metadata extraction - Book URL: $bookUrl');
    AnxLog.info('Index HTML path: $indexHtmlPath');

    AnxLog.info('Creating HeadlessInAppWebView for iOS platform');

    HeadlessInAppWebView webview = HeadlessInAppWebView(
      webViewEnvironment: webViewEnvironment,
      initialUrlRequest: URLRequest(url: WebUri(indexHtmlPath)),
      onLoadStart: (controller, url) async {
        AnxLog.info('WebView load started: $url');
      },
      onLoadStop: (controller, url) async {
        AnxLog.info('WebView load stopped: $url');
      },
      onReceivedError: (controller, request, error) async {
        AnxLog.severe(
          'WebView load error: ${error.description} (code: ${error.type}) for URL: ${request.url}',
        );
      },
      onConsoleMessage: (controller, consoleMessage) {
        AnxLog.info(
          'WebView console: ${consoleMessage.messageLevel.name}: ${consoleMessage.message}',
        );

        if (consoleMessage.message.contains('loadBook')) {
          controller.addJavaScriptHandler(
            handlerName: 'onMetadata',
            callback: (List<dynamic> args) async {
              final metadata = args[0] as Map<String, dynamic>;
              final title = metadata['title'] as String? ?? 'Unknown';
              final dynamic authorData = metadata['author'];
              final String author;

              if (authorData is String) {
                author = authorData;
              } else if (authorData is List) {
                author = authorData
                    .map((dynamic authorItem) {
                      if (authorItem is String) {
                        return authorItem;
                      } else if (authorItem is Map<String, dynamic>) {
                        return authorItem['name'] as String? ?? '';
                      }
                      return '';
                    })
                    .where((String name) => name.isNotEmpty)
                    .join(', ');
              } else {
                author = 'Unknown';
              }

              // base64 cover
              final cover = metadata['cover'] as String? ?? '';
              final description = metadata['description'] as String? ?? '';
              saveBook(file, title, author, description, cover);
              ref?.read(bookListProvider.notifier).refresh();
              // return;
            },
          );
          webviewInitialVariable(controller, bookUrl, cfi, importing: true);
        }
        if (consoleMessage.messageLevel == ConsoleMessageLevel.ERROR) {
          headlessInAppWebView?.dispose();
          headlessInAppWebView = null;
          // throw Exception('Webview: ${consoleMessage.message}');
        }
        webviewConsoleMessage(controller, consoleMessage);
      },
    );

    await webview.dispose();
    AnxLog.info('Starting WebView...');
    await webview.run();
    headlessInAppWebView = webview;

    AnxLog.info('WebView started, waiting for metadata extraction...');

    // max 60s with progress logging (increased for iOS WebView performance)
    int count = 0;
    const maxCount = 600; // 60 seconds
    while (count < maxCount) {
      if (headlessInAppWebView == null) {
        AnxLog.info('WebView completed successfully');
        return;
      }

      // Log progress every 5 seconds
      if (count % 50 == 0 && count > 0) {
        final elapsed = count * 100;
        AnxLog.info(
          'WebView processing... ${elapsed}ms elapsed (${(elapsed / 1000).toStringAsFixed(1)}s)',
        );
      }

      await Future<void>.delayed(const Duration(milliseconds: 100));
      count++;
    }

    AnxLog.severe('WebView timeout after 60 seconds - disposing WebView');
    headlessInAppWebView?.dispose();
    headlessInAppWebView = null;
    throw Exception('Import: Get book metadata timeout after 60 seconds');
  } catch (e, stackTrace) {
    AnxLog.severe('Error in getBookMetadata: $e\nStack trace: $stackTrace');

    // Clean up WebView
    headlessInAppWebView?.dispose();
    headlessInAppWebView = null;

    // Re-throw with more context
    throw Exception('Failed to extract book metadata: $e');
  }
}
