import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'platform_adaptations.dart';

/// Platform-specific icon system for Dasso Reader
///
/// Provides platform-appropriate icons that enhance the native feel
/// while maintaining consistency with the app's design system.
class AdaptiveIcons {
  // =====================================================
  // NAVIGATION ICONS
  // =====================================================

  /// Platform-appropriate back icon
  static IconData get back {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.back;
    }
    return Icons.arrow_back;
  }

  /// Platform-appropriate forward icon
  static IconData get forward {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.forward;
    }
    return Icons.arrow_forward;
  }

  /// Platform-appropriate close icon
  static IconData get close {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.xmark;
    }
    return Icons.close;
  }

  /// Platform-appropriate menu icon
  static IconData get menu {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.line_horizontal_3;
    }
    return Icons.menu;
  }

  /// Platform-appropriate more options icon
  static IconData get moreOptions {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.ellipsis;
    }
    return Icons.more_vert;
  }

  // =====================================================
  // ACTION ICONS
  // =====================================================

  /// Platform-appropriate add icon
  static IconData get add {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.add;
    }
    return Icons.add;
  }

  /// Platform-appropriate delete icon
  static IconData get delete {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.delete;
    }
    return Icons.delete_outline;
  }

  /// Platform-appropriate edit icon
  static IconData get edit {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.pencil;
    }
    return Icons.edit_outlined;
  }

  /// Platform-appropriate save icon
  static IconData get save {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.checkmark;
    }
    return Icons.save_outlined;
  }

  /// Platform-appropriate share icon
  static IconData get share {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.share;
    }
    return Icons.share_outlined;
  }

  /// Platform-appropriate copy icon
  static IconData get copy {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.doc_on_doc;
    }
    return Icons.copy_outlined;
  }

  // =====================================================
  // CONTENT ICONS
  // =====================================================

  /// Platform-appropriate search icon
  static IconData get search {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.search;
    }
    return Icons.search;
  }

  /// Platform-appropriate book icon
  static IconData get book {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.book;
    }
    return Icons.menu_book_outlined;
  }

  /// Platform-appropriate bookmark icon
  static IconData get bookmark {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.bookmark;
    }
    return Icons.bookmark_outline;
  }

  /// Platform-appropriate bookmark filled icon
  static IconData get bookmarkFilled {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.bookmark_fill;
    }
    return Icons.bookmark;
  }

  /// Platform-appropriate note icon
  static IconData get note {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.doc_text;
    }
    return Icons.note_outlined;
  }

  // =====================================================
  // MEDIA ICONS
  // =====================================================

  /// Platform-appropriate play icon
  static IconData get play {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.play_fill;
    }
    return Icons.play_arrow;
  }

  /// Platform-appropriate pause icon
  static IconData get pause {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.pause_fill;
    }
    return Icons.pause;
  }

  /// Platform-appropriate stop icon
  static IconData get stop {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.stop_fill;
    }
    return Icons.stop;
  }

  /// Platform-appropriate volume icon
  static IconData get volume {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.speaker_2;
    }
    return Icons.volume_up_outlined;
  }

  // =====================================================
  // SETTINGS ICONS
  // =====================================================

  /// Platform-appropriate settings icon
  static IconData get settings {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.settings;
    }
    return Icons.settings_outlined;
  }

  /// Platform-appropriate profile icon
  static IconData get profile {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.person;
    }
    return Icons.person_outline;
  }

  /// Platform-appropriate info icon
  static IconData get info {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.info;
    }
    return Icons.info_outline;
  }

  // =====================================================
  // STATUS ICONS
  // =====================================================

  /// Platform-appropriate success icon
  static IconData get success {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.checkmark_circle_fill;
    }
    return Icons.check_circle;
  }

  /// Platform-appropriate error icon
  static IconData get error {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.exclamationmark_circle_fill;
    }
    return Icons.error;
  }

  /// Platform-appropriate warning icon
  static IconData get warning {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.exclamationmark_triangle_fill;
    }
    return Icons.warning;
  }

  // =====================================================
  // LEARNING ICONS (Chinese Learning Specific)
  // =====================================================

  /// Platform-appropriate dictionary icon
  static IconData get dictionary {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.book; // Use basic book icon instead of book_circle
    }
    return Icons.translate;
  }

  /// Platform-appropriate vocabulary icon
  static IconData get vocabulary {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons
          .textformat_abc; // Use textformat_abc instead of textformat
    }
    return Icons.menu_book; // Changed from rounded to standard for consistency
  }

  /// Platform-appropriate HSK icon
  static IconData get hsk {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.book_solid; // Use book_solid instead of book_circle
    }
    return Icons.school; // Changed from rounded to standard for consistency
  }

  /// Platform-appropriate audio icon
  static IconData get audio {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.speaker_1;
    }
    return Icons.volume_up;
  }

  // =====================================================
  // UTILITY METHODS
  // =====================================================

  /// Creates platform-appropriate icon widget
  static Widget createAdaptiveIcon(
    IconData iconData, {
    double? size,
    Color? color,
  }) {
    return Icon(
      iconData,
      size: size,
      color: color,
    );
  }

  /// Creates platform-appropriate icon button
  static Widget createAdaptiveIconButton({
    required IconData icon,
    required VoidCallback onPressed,
    double? size,
    Color? color,
    String? tooltip,
  }) {
    return IconButton(
      icon: Icon(icon),
      onPressed: onPressed,
      iconSize: size,
      color: color,
      tooltip: tooltip,
    );
  }

  /// Returns platform-appropriate chevron icon for navigation
  static IconData get chevronRight {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.chevron_right;
    }
    return Icons.chevron_right;
  }

  /// Returns platform-appropriate chevron icon for dropdowns
  static IconData get chevronDown {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.chevron_down;
    }
    return Icons.keyboard_arrow_down;
  }

  /// Returns platform-appropriate home icon
  static IconData get home {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.house;
    }
    return Icons.home_outlined;
  }
}
